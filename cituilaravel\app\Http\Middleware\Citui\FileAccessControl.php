<?php
declare(strict_types=1);

namespace App\Http\Middleware\Citui;

use Closure;
use Illuminate\Http\Request;
use App\Models\Citui\File;
use App\Models\Citui\FileRelation;

class FileAccessControl
{
    /**
     * 处理传入的请求
     */
    public function handle(Request $request, Closure $next, string $accessType = 'public')
    {
        $fileId = $request->route('fileId') ?? $request->input('file_id');
        
        if (!$fileId) {
            return response()->json([
                'status' => 400,
                'code' => 0,
                'msg' => '文件ID不能为空',
                'data' => null
            ], 400);
        }
        
        $file = File::find($fileId);
        
        if (!$file) {
            return response()->json([
                'status' => 404,
                'code' => 0,
                'msg' => '文件不存在',
                'data' => null
            ], 404);
        }
        
        if ($file->is_deleted) {
            return response()->json([
                'status' => 404,
                'code' => 0,
                'msg' => '文件已被删除',
                'data' => null
            ], 404);
        }
        
        // 检查访问权限
        if (!$this->checkAccess($file, $request, $accessType)) {
            return response()->json([
                'status' => 403,
                'code' => 0,
                'msg' => '没有访问权限',
                'data' => null
            ], 403);
        }
        
        // 将文件信息添加到请求中
        $request->attributes->set('file', $file);
        
        return $next($request);
    }
    
    /**
     * 检查文件访问权限
     */
    protected function checkAccess(File $file, Request $request, string $accessType): bool
    {
        $user = $request->user();
        $userId = $user ? $user->user_id : null;
        
        switch ($accessType) {
            case 'public':
                // 公开文件，任何人都可以访问
                return $file->is_public;
                
            case 'owner':
                // 只有文件所有者可以访问
                return $userId && $file->uploader_id === $userId;
                
            case 'authenticated':
                // 需要登录用户才能访问
                if (!$userId) {
                    return false;
                }
                
                // 如果是公开文件，登录用户都可以访问
                if ($file->is_public) {
                    return true;
                }
                
                // 如果是私有文件，只有所有者可以访问
                return $file->uploader_id === $userId;
                
            case 'business':
                // 业务相关的文件访问控制
                return $this->checkBusinessAccess($file, $request, $userId);
                
            case 'admin':
                // 管理员权限
                return $this->checkAdminAccess($user);
                
            default:
                return false;
        }
    }
    
    /**
     * 检查业务相关的文件访问权限
     */
    protected function checkBusinessAccess(File $file, Request $request, ?int $userId): bool
    {
        if (!$userId) {
            return false;
        }
        
        // 如果是文件所有者，直接允许访问
        if ($file->uploader_id === $userId) {
            return true;
        }
        
        // 检查文件关联的业务对象权限
        $relations = FileRelation::where('file_id', $file->file_id)->get();
        
        foreach ($relations as $relation) {
            if ($this->checkBusinessObjectAccess($relation, $userId)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检查业务对象访问权限
     */
    protected function checkBusinessObjectAccess(FileRelation $relation, int $userId): bool
    {
        switch ($relation->business_type) {
            case 'evaluation_report':
                // 评测报告：作者或公开报告
                $report = \App\Models\Citui\EvaluationReport::find($relation->business_id);
                return $report && ($report->user_id === $userId || $report->status === 'approved');
                
            case 'water_clue':
                // 线索：作者或公开线索
                $clue = \App\Models\Citui\WaterClue::find($relation->business_id);
                return $clue && ($clue->user_id === $userId || $clue->status === 'approved');
                
            case 'user_avatar':
                // 用户头像：用户本人或公开头像
                $user = \App\Models\Citui\User::find($relation->business_id);
                return $user && ($user->user_id === $userId || true); // 头像通常是公开的
                
            default:
                return false;
        }
    }
    
    /**
     * 检查管理员权限
     */
    protected function checkAdminAccess($user): bool
    {
        if (!$user) {
            return false;
        }
        
        // 检查是否为管理员用户
        if (method_exists($user, 'isAdmin')) {
            return $user->isAdmin();
        }
        
        // 检查是否为管理员表中的用户
        $adminUser = \App\Models\Citui\AdminUser::where('user_id', $user->user_id)
                                                ->where('status', 'active')
                                                ->first();
        
        return $adminUser !== null;
    }
    
    /**
     * 记录文件访问日志
     */
    protected function logFileAccess(File $file, Request $request, bool $allowed): void
    {
        try {
            \App\Models\Citui\OperationLog::create([
                'user_id' => $request->user() ? $request->user()->user_id : null,
                'action' => 'file_access',
                'data' => json_encode([
                    'file_id' => $file->file_id,
                    'file_name' => $file->original_name,
                    'allowed' => $allowed,
                    'ip_address' => $request->ip(),
                    'user_agent' => $request->userAgent()
                ]),
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);
        } catch (\Exception $e) {
            // 记录日志失败不影响主流程
            \Log::warning('Failed to log file access: ' . $e->getMessage());
        }
    }
}