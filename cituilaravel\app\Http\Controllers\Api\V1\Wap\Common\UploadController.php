<?php

namespace App\Http\Controllers\Api\V1\Wap\Common;

use App\Http\Controllers\Api\Controller;
use App\Utils\Upload as UploadFile;

class UploadController extends Controller
{
    protected UploadFile $uploadService;
    public function __construct(UploadFile $upload)
    {
        $this->uploadService = $upload;
        parent::__construct();
    }

    public function upload(string $type='image')
    {
        $type = strtolower($type);
        $type = in_array($type,['image','file','video','audio']) ? $type : 'image';
        $res = $this->uploadService->upload($type);
        return $this->apiSuccess([$res['path']??'']);
    }
}
