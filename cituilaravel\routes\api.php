<?php
declare(strict_types=1);

use App\Http\Controllers\Api\V1\Wap;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

//广告奖励回调地址
Route::namespace('Api')->prefix('ad_reward')->group(function(){
    Route::get('gromore_x4gjvqcdgu',[Wap\User\UserController::class,'rewardGromoreCallback']);
    Route::get('gdt_cvwfn8tvwu',[Wap\User\UserController::class,'rewardGdtCallback']);

    Route::get('gromore',[Wap\User\UserController::class,'rewardGromoreCallback']);
    Route::get('gdt',[Wap\User\UserController::class,'rewardGdtCallback']);
    Route::get('ks',[Wap\User\UserController::class,'rewardKsCallback']);
});


Route::namespace('Api')->group(function () {
    Route::get('captcha',[Wap\Common\CaptchaController::class,'getCaptcha']);
    Route::post('login/loginSms',[Wap\User\UserController::class,'loginSms']);
    Route::post('user/register',[Wap\User\UserController::class,'register']);
    Route::post('user/reg_h5',[Wap\User\UserController::class,'regH5']);

    Route::get('index',[Wap\Index\IndexController::class,'index']);
    Route::get('game/list',[Wap\Index\IndexController::class,'gameList']);

    Route::match(['get', 'post'], 'Config/getProtocol',[Wap\Index\IndexController::class,'getProtocol']);

});

Route::namespace('Api')->middleware('userLogin')->group(function(){
    Route::post('upload/uploadImage',[Wap\Common\UploadController::class,'upload']);
    Route::get('user/info',[Wap\User\UserController::class,'getUserInfo']);
    Route::get('user/getUser',[Wap\User\UserController::class,'getUser']);



});
