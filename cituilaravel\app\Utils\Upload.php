<?php
declare(strict_types=1);

namespace App\Utils;

use App\Exceptions\MyException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class Upload
{
    protected $arrAllowExtension = [];

    protected Request $request;
    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    public function upload(string $type='')
    {
        if(empty($type)){
            $type = 'image';
        }
        $this->arrAllowExtension = config('jk.allowExtension.'.$type);

        $obFile = $this->request->file('file');
        if(!$obFile)
        {
            throw new MyException("请选择文件");
        }

        $sOldName   = $obFile->getClientOriginalName();//原名

        $sFileType  = $obFile->getClientMimeType(); //文件类型

        $sExtensionName = strtolower($obFile->getClientOriginalExtension());//扩展名

        $sPath      = $obFile->getRealPath();    //文件存储的位置路径

        //验证文件大小不超过2MB
        if($obFile->getSize() > 2 * 1024 * 1024)
        {
            throw new MyException("文件大小不能超过2MB");
        }

        if (!in_array($sExtensionName,$this->arrAllowExtension) )
        {
            throw new MyException("文件格式不支持");
        }

        //组装文件存储的位置和自定义文件名
        $sFileName = 'upload/image/' . date('Ymd') . '/' . $this->saveFileName() . '.' .$sExtensionName;

        $bRet = Storage::disk('upload')->put($sFileName,file_get_contents($sPath));

        if (!$bRet)
        {
            throw new MyException("上传失败");
        }
        return ['url'=>uploadFilePathNoPre($sFileName),'path'=>'/'.$sFileName];
    }

    public function saveFileName(): string
    {
        return time().mt_rand(1000,9999).generateRandomString(4);
    }

    public function delete()
    {
       $path = $this->request->input('path');
       if(empty($path)) return [];
       return $this->deleteFile($path);
    }

    public function deleteFile(string $path='')
    {
        $path = str_ireplace([config('app.url')],'',$path);
        if(!Str::startsWith($path,'/')){
            $path = '/'.$path;
        }
        @unlink(public_path($path));
        return [];
    }



}
