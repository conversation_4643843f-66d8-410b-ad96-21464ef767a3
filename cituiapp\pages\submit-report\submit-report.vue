<template>
	<view class="page-container">
		<!-- 导航栏 -->
		<view class="navbar">
			<view class="nav-left" @click="goBack">
				<u-icon name="arrow-left" size="20" color="#333"></u-icon>
			</view>
			<view class="nav-title">提交评测报告</view>
			<view class="nav-right"></view>
		</view>
		
		<!-- 表单内容 -->
		<scroll-view class="content-container" scroll-y>
			<view class="form-container">
				<!-- APP Logo上传 -->
				<view class="form-item">
					<view class="form-label">上传APP Logo</view>
					<ImageUploader 
						v-model="logoUrl"
						placeholder="点击上传APP Logo"
						tip="支持JPG、PNG格式，尺寸建议512x512"
						@change="onLogoChange"
					/>
				</view>
				
				<!-- APP名称 -->
				<view class="form-item">
					<view class="form-label">APP名称</view>
					<u-input 
						v-model="formData.appName" 
						placeholder="请输入APP的完整名称"
						border="surround"
						:clearable="true"
					></u-input>
				</view>
				
				<!-- 评分 -->
				<view class="form-item">
					<view class="form-label">评分（1-5分）</view>
					<u-rate 
						v-model="formData.rating" 
						:count="5" 
						:size="24"
						:disabled="false"
						active-color="#ffa500"
						inactive-color="#d1d5db"
					></u-rate>
					<text class="form-tip">点击星星进行评分</text>
				</view>
				
				<!-- APP类型 -->
				<view class="form-item">
					<view class="form-label">APP类型</view>
					<view class="radio-grid">
						<view 
							v-for="(type, index) in appTypes" 
							:key="index"
							class="radio-item"
							:class="{ 'active': formData.appType === type }"
							@click="selectAppType(type)"
						>
							<text>{{ type }}</text>
						</view>
					</view>
				</view>
				
				<!-- 运行模式 -->
				<view class="form-item">
					<view class="form-label">运行模式</view>
					<view class="radio-row">
						<view 
							v-for="(mode, index) in runModes" 
							:key="index"
							class="radio-item"
							:class="{ 'active': formData.runMode === mode.value }"
							@click="selectRunMode(mode.value)"
						>
							<text>{{ mode.label }}</text>
						</view>
					</view>
				</view>
				
				<!-- 新人福利 -->
				<view class="form-item">
					<view class="form-label">新人福利(元)</view>
					<view class="input-with-prefix">
						<u-input 
							v-model="formData.newUserBenefit" 
							placeholder="0.00"
							type="number"
							border="surround"
						></u-input>
					</view>
				</view>
				<!-- 提现门槛 -->
				<view class="form-item">
					<view class="form-label">提现门槛(元)</view>
					<view class="input-with-prefix">
						<u-input 
							v-model="formData.withdrawThreshold" 
							placeholder="0.00"
							type="number"
							border="surround"
						></u-input>
					</view>
				</view>
				
				<!-- 顶包金额 -->
				<view class="form-item">
					<view class="form-label">顶包金额(元)</view>
					<view class="input-with-prefix">
						<u-input 
							v-model="formData.topPackageAmount" 
							placeholder="0.00"
							type="number"
							border="surround"
						></u-input>
					</view>
				</view>
				
				<!-- 测试条数 -->
				<view class="form-item">
					<view class="form-label">测试条数</view>
					<view class="radio-row">
						<view 
							v-for="count in testCounts" 
							:key="count"
							class="radio-item"
							:class="{ 'active': formData.testCount === count }"
							@click="selectTestCount(count)"
						>
							<text>{{ count }}条</text>
						</view>
					</view>
				</view>
				
				<!-- 测试收益 -->
				<view class="form-item">
					<view class="form-label">测试总收益(元)</view>
					<view class="input-with-prefix">
						<u-input 
							v-model="formData.testRevenue" 
							placeholder="0.00"
							type="number"
							border="surround"
						></u-input>
					</view>
				</view>
				
				<!-- 测试时长 -->
				<view class="form-item">
					<view class="form-label">测试时长(分钟)</view>
					<u-input 
						v-model="formData.testDuration" 
						placeholder="如：10"
						type="number"
						border="surround"
					></u-input>
				</view>
				
				<!-- 测试设备 -->
				<view class="form-item">
					<view class="form-label">测试设备</view>
					<u-input 
						v-model="formData.testDevice" 
						placeholder="如：iPhone 15 Pro / 华为mate40"
						border="surround"
						:clearable="true"
					></u-input>
				</view>
				
				<!-- 测评人和测试时间 -->
				<view class="form-row">
					<view class="form-item flex-1">
						<view class="form-label">测评人</view>
						<u-input 
							v-model="formData.evaluator" 
							placeholder="输入测评人姓名"
							border="surround"
							:clearable="true"
						></u-input>
					</view>
					<view class="form-item flex-1">
						<view class="form-label">测试日期</view>
						<u-datetime-picker 
							v-model="formData.testDate"
							mode="date"
							:show="showDatePicker"
							@confirm="confirmDate"
							@cancel="showDatePicker = false"
						></u-datetime-picker>
						<u-input 
							v-model="formData.testDateText"
							placeholder="选择日期"
							border="surround"
							readonly
							@click="showDatePicker = true"
						></u-input>
					</view>
				</view>
				
				<!-- 收益明细 -->
				<view class="form-item">
					<view class="form-label">各条收益明细（用于生成曲线图）</view>
					<view class="price-inputs">
						<view 
							v-for="(row, rowIndex) in Math.ceil(formData.testCount / 2)" 
							:key="rowIndex"
							class="price-row"
						>
							<view 
								v-for="(col, colIndex) in 2" 
								:key="colIndex"
								class="price-input-wrapper"
							>
								<view 
									v-if="rowIndex * 2 + colIndex < formData.testCount"
									class="price-input-item"
								>
									<u-input 
										v-model="formData.priceDetails[rowIndex * 2 + colIndex]"
										:placeholder="`第${rowIndex * 2 + colIndex + 1}条价格`"
										type="number"
										border="surround"
									></u-input>
								</view>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 游戏主界面截图 -->
				<view class="form-item">
					<view class="form-label">游戏主界面截图</view>
					<ImageUploader 
						v-model="gameScreenshotUrl"
						placeholder="点击上传游戏主界面截图"
						tip="支持JPG、PNG格式"
						@change="onGameScreenshotChange"
					/>
				</view>
				
				<!-- APP内提现记录截图 -->
				<view class="form-item">
					<view class="form-label">APP内提现记录截图</view>
					<ImageUploader 
						v-model="withdrawScreenshotUrl"
						placeholder="点击上传APP内提现记录截图"
						tip="支持JPG、PNG格式"
						@change="onWithdrawScreenshotChange"
					/>
				</view>
				
				<!-- 微信到账记录截图 -->
				<view class="form-item">
					<view class="form-label">微信到账记录截图</view>
					<ImageUploader 
						v-model="wechatScreenshotUrl"
						placeholder="点击上传微信到账记录截图"
						tip="支持JPG、PNG格式"
						@change="onWechatScreenshotChange"
					/>
				</view>
				
				<!-- 测评报告 -->
				<view class="form-item">
					<view class="form-label">测评报告</view>
					<u-textarea 
						v-model="formData.evaluationReport"
						placeholder="详细描述测评情况，包括使用体验、收益情况、提现速度等..."
						:maxlength="500"
						:showWordLimit="true"
						:autoHeight="true"
						border="surround"
					></u-textarea>
				</view>
				
				<!-- 提交按钮 -->
				<view class="submit-section">
					<u-button 
						type="primary"
						size="large"
						:loading="isSubmitting"
						:disabled="isSubmitting"
						@click="handleSubmit"
						shape="round"
						customStyle="background: linear-gradient(135deg, #3b82f6, #2563eb); border: none;"
					>
						{{ isSubmitting ? '提交中...' : '提交评测报告（+50积分）' }}
					</u-button>
					<text class="submit-tip">提交并审核通过后将获得50积分奖励</text>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	import ImageUploader from '@/components/ImageUploader.vue'
	
	export default {
		components: {
			ImageUploader
		},
		data() {
			return {
				logoUrl: '',
				gameScreenshotUrl: '',
				withdrawScreenshotUrl: '',
				wechatScreenshotUrl: '',
				showDatePicker: false,
				isSubmitting: false,
				submitTimer: null, // 防抖定时器
				
				formData: {
					appName: '',
					rating: 4,
					appType: '',
					runMode: '',
					newUserBenefit: '',
					withdrawThreshold: '',
					topPackageAmount: '',
					testCount: 5,
					testRevenue: '',
					testDuration: '',
					testDevice: '',
					evaluator: '',
					testDate: Number(new Date()),
					testDateText: '',
					priceDetails: ['', '', '', '', '', '', '', '', '', ''],
					evaluationReport: ''
				},
				
				appTypes: ['合成游戏', '短剧', '阅读', '走路', '答题', '其他'],
				runModes: [
					{ label: '自动', value: '自动', icon: 'robot' },
					{ label: '手动', value: '手动', icon: 'hand-open' }
				],
				testCounts: [5, 10]
			}
		},
		
		methods: {
			// 返回上一页
			goBack() {
				uni.navigateBack()
			},
			
			// 选择APP类型
			selectAppType(type) {
				this.formData.appType = type
			},
			
			// 选择运行模式
			selectRunMode(mode) {
				this.formData.runMode = mode
			},
			
			// 选择测试条数
			selectTestCount(count) {
				this.formData.testCount = count
				// 重置价格明细数组
				this.formData.priceDetails = new Array(count).fill('')
			},
			
			// 确认日期选择
			confirmDate(e) {
				this.formData.testDate = e.value
				this.formData.testDateText = uni.$u.timeFormat(e.value, 'yyyy-mm-dd')
				this.showDatePicker = false
			},
			
			// 图片上传回调方法
			onLogoChange(imageUrl, imagePath) {
				console.log('Logo上传成功:', imageUrl, imagePath)
				// 可以在这里添加额外的处理逻辑
			},
			
			onGameScreenshotChange(imageUrl, imagePath) {
				console.log('游戏截图上传成功:', imageUrl, imagePath)
			},
			
			onWithdrawScreenshotChange(imageUrl, imagePath) {
				console.log('提现记录截图上传成功:', imageUrl, imagePath)
			},
			
			onWechatScreenshotChange(imageUrl, imagePath) {
				console.log('微信截图上传成功:', imageUrl, imagePath)
			},
			
			// 表单验证
			validateForm() {
				if (!this.formData.appName) {
					uni.showToast({ title: '请输入APP名称', icon: 'none' })
					return false
				}
				
				if (!this.formData.appType) {
					uni.showToast({ title: '请选择APP类型', icon: 'none' })
					return false
				}
				
				if (!this.formData.runMode) {
					uni.showToast({ title: '请选择运行模式', icon: 'none' })
					return false
				}
				
				if (!this.logoUrl) {
					uni.showToast({ title: '请上传APP Logo', icon: 'none' })
					return false
				}
				
				return true
			},
			
			// 提交表单（带防抖）
			handleSubmit() {
				// 防抖处理
				if (this.submitTimer) {
					clearTimeout(this.submitTimer)
				}
				
				this.submitTimer = setTimeout(() => {
					this.submitForm()
				}, 300)
			},
			
			// 实际提交表单
			async submitForm() {
				if (this.isSubmitting) return
				
				if (!this.validateForm()) return
				
				this.isSubmitting = true
				
				try {
					// 这里应该调用实际的提交接口
					await new Promise(resolve => setTimeout(resolve, 2000)) // 模拟网络请求
					
					uni.showToast({
						title: '提交成功',
						icon: 'success',
						duration: 2000
					})
					
					// 提交成功后返回上一页
					setTimeout(() => {
						uni.navigateBack()
					}, 2000)
					
				} catch (error) {
					uni.showToast({
						title: '提交失败，请重试',
						icon: 'error'
					})
				} finally {
					this.isSubmitting = false
				}
			}
		},
		
		onLoad() {
			// 初始化日期为今天
			this.formData.testDateText = uni.$u.timeFormat(new Date(), 'yyyy-mm-dd')
		},
		
		onUnload() {
			// 清理定时器
			if (this.submitTimer) {
				clearTimeout(this.submitTimer)
			}
		}
	}
</script>

<style lang="scss" scoped>
.page-container {
	width: 100%;
	min-height: 100vh;
	background-color: #ffffff;
}

.navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 999;
	height: 88rpx;
	background-color: #ffffff;
	border-bottom: 1rpx solid #f0f0f0;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 32rpx;
	
	.nav-left, .nav-right {
		width: 80rpx;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.nav-title {
		flex: 1;
		text-align: center;
		font-size: 32rpx;
		font-weight: 600;
		color: #333333;
	}
}

.content-container {
	padding-top: 88rpx;
	height: calc(100vh - 88rpx);
}

.form-container {
	padding: 32rpx;
}

.form-item {
	margin-bottom: 40rpx;
	
	.form-label {
		font-size: 28rpx;
		font-weight: 500;
		color: #374151;
		margin-bottom: 16rpx;
		display: block;
	}
	
	.form-tip {
		font-size: 24rpx;
		color: #6b7280;
		margin-top: 8rpx;
		display: block;
	}
}

.form-row {
	display: flex;
	gap: 24rpx;
	margin-bottom: 40rpx;
	
	.flex-1 {
		flex: 1;
	}
}

/* 上传容器样式已移至ImageUploader组件中 */

.radio-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 16rpx;
}

.radio-row {
	display: flex;
	gap: 16rpx;
}

.radio-item {
	flex: 1;
	border: 2rpx solid #d1d5db;
	border-radius: 16rpx;
	padding: 24rpx 16rpx;
	text-align: center;
	background-color: #ffffff;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 8rpx;
	transition: all 0.3s ease;
	
	text {
		font-size: 28rpx;
		color: #374151;
	}
	
	&.active {
		border-color: #3b82f6;
		background-color: #dbeafe;
		
		text {
			color: #3b82f6;
			font-weight: 500;
		}
	}
	
	&:active {
		transform: scale(0.98);
	}
}

.input-with-prefix {
	position: relative;
	
	.input-prefix {
		position: absolute;
		left: 24rpx;
		top: 50%;
		transform: translateY(-50%);
		font-size: 28rpx;
		color: #6b7280;
		z-index: 10;
	}
	
	:deep(.u-input) {
		padding-left: 56rpx !important;
	}
}

.price-inputs {
	.price-row {
		display: flex;
		gap: 16rpx;
		margin-bottom: 16rpx;
		
		.price-input-wrapper {
			flex: 1;
			
			.price-input-item {
				width: 100%;
			}
		}
	}
}

.submit-section {
	margin-top: 60rpx;
	margin-bottom: 60rpx;
	
	.submit-tip {
		display: block;
		text-align: center;
		font-size: 24rpx;
		color: #6b7280;
		margin-top: 16rpx;
	}
}

/* uview组件样式覆盖 */
:deep(.u-input__content) {
	background-color: #ffffff !important;
	border-radius: 16rpx !important;
	border: 2rpx solid #d1d5db !important;
	padding: 24rpx !important;
}

:deep(.u-textarea) {
	background-color: #ffffff !important;
	border-radius: 16rpx !important;
	border: 2rpx solid #d1d5db !important;
	padding: 24rpx !important;
}

:deep(.u-button) {
	height: 96rpx !important;
	font-size: 32rpx !important;
	font-weight: 600 !important;
}
</style> 