<template>
	<view class="image-uploader">
		<view class="upload-container" @click="triggerUpload">
			<view v-if="!imageUrl" class="upload-placeholder">
				<u-icon name="photo" size="40" color="#999"></u-icon>
				<text class="upload-text">{{ placeholder || '点击上传图片' }}</text>
				<text class="upload-tip">{{ tip || '支持JPG、PNG格式，大小不超过2MB' }}</text>
			</view>
			<image 
				v-else 
				:src="imageUrl" 
				class="uploaded-image" 
				mode="aspectFit"
				@load="onImageLoad"
				@error="onImageError"
			></image>
			
			<!-- 上传进度遮罩 -->
			<view v-if="uploading" class="upload-mask">
				<u-loading-icon mode="circle" color="#fff" size="24"></u-loading-icon>
				<text class="upload-progress">上传中...{{ uploadProgress }}%</text>
			</view>
			
			<!-- 删除按钮 -->
			<view v-if="imageUrl && showDelete" class="delete-btn" @click.stop="deleteImage">
				<u-icon name="close" size="16" color="#fff"></u-icon>
			</view>
		</view>
	</view>
</template>

<script>
import { getToken } from '@/utils/storage.js';
import { IMG_DOMAIN, DOMAIN } from '@/env.js';

export default {
	name: 'ImageUploader',
	props: {
		// 当前图片URL
		value: {
			type: String,
			default: ''
		},
		// 占位符文本
		placeholder: {
			type: String,
			default: '点击上传图片'
		},
		// 提示文本
		tip: {
			type: String,
			default: '支持JPG、PNG格式，大小不超过2MB'
		},
		// 是否显示删除按钮
		showDelete: {
			type: Boolean,
			default: true
		},
		// 上传容器高度
		height: {
			type: String,
			default: '240rpx'
		},
		// 是否禁用
		disabled: {
			type: Boolean,
			default: false
		}
	},
	
	data() {
		return {
			imageUrl: '',
			uploading: false,
			uploadProgress: 0
		}
	},
	
	watch: {
		value: {
			handler(newVal) {
				// 如果传入的是相对路径，需要构建完整URL
				if (newVal && !newVal.startsWith('http://') && !newVal.startsWith('https://')) {
					this.imageUrl = this.buildImageUrl(newVal);
				} else {
					this.imageUrl = newVal;
				}
			},
			immediate: true
		}
	},
	
	methods: {
		// 触发图片选择
		triggerUpload() {
			if (this.disabled || this.uploading) return;
			
			uni.chooseImage({
				count: 1,
				sizeType: ['compressed'],
				sourceType: ['camera', 'album'],
				success: (res) => {
					const tempFilePath = res.tempFilePaths[0];
					this.uploadImage(tempFilePath);
				},
				fail: (err) => {
				}
			});
		},
		
		// 上传图片到服务器
		async uploadImage(filePath) {
			const token = getToken();
			if (!token) {
				uni.showToast({
					title: '请先登录',
					icon: 'none'
				});
				return;
			}
			
			this.uploading = true;
			this.uploadProgress = 0;
			
			try {
				const uploadTask = uni.uploadFile({
					url: uni.$u.http.config.baseURL + '/upload/uploadImage',
					filePath: filePath,
					name: 'file',
					header: {
						'Authorization': `Bearer ${token}`
					},
					success: (res) => {
						try {
							const data = JSON.parse(res.data);
							if (data.status === 200 && data.code === 1) {
								const imagePath = data.data;
								// 构建完整的图片URL
								let fullImageUrl = this.buildImageUrl(imagePath);
								this.imageUrl = fullImageUrl;
								
								console.log('图片上传成功 - 原始路径:', imagePath);
								console.log('图片上传成功 - 完整URL:', fullImageUrl);
								console.log('图片上传成功 - IMG_DOMAIN:', IMG_DOMAIN);
								
								// 添加调试信息
								console.log('=== 图片上传调试信息 ===');
								console.log('当前环境:', process.env.NODE_ENV);
								console.log('DOMAIN:', DOMAIN);
								console.log('IMG_DOMAIN:', IMG_DOMAIN);
								console.log('原始路径:', imagePath);
								console.log('完整URL:', fullImageUrl);
								console.log('========================');
								
								// 验证图片URL是否可访问
								this.validateImageUrl(fullImageUrl);
								
								// 触发父组件更新
								this.$emit('input', this.imageUrl);
								this.$emit('change', this.imageUrl, imagePath);
								
								uni.showToast({
									title: '上传成功',
									icon: 'success'
								});
							} else {
								throw new Error(data.msg || '上传失败');
							}
						} catch (parseError) {
							console.error('解析响应失败:', parseError);
							console.error('响应内容:', res.data);
							throw new Error('上传响应解析失败');
						}
					},
					fail: (err) => {
						console.error('上传失败:', err);
						throw new Error('上传失败');
					}
				});
				
				// 监听上传进度
				uploadTask.onProgressUpdate((res) => {
					this.uploadProgress = res.progress;
				});
				
			} catch (error) {
				console.error('上传错误:', error);
				uni.showToast({
					title: error.message || '上传失败',
					icon: 'none'
				});
			} finally {
				this.uploading = false;
				this.uploadProgress = 0;
			}
		},
		
		// 构建图片URL
		buildImageUrl(imagePath) {
			if (!imagePath) return '';
			
			// 如果已经是完整URL，直接返回
			if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
				return imagePath;
			}
			
			// 获取当前环境的图片域名
			let imgDomain = IMG_DOMAIN;
			
			// 确保路径以斜杠开头
			let normalizedPath = imagePath;
			if (!normalizedPath.startsWith('/')) {
				normalizedPath = '/' + normalizedPath;
			}
			
			// 拼接完整URL，避免双斜杠
			let fullUrl;
			if (imgDomain.endsWith('/')) {
				fullUrl = imgDomain.slice(0, -1) + normalizedPath;
			} else {
				fullUrl = imgDomain + normalizedPath;
			}
			
			console.log('构建图片URL:', {
				原始路径: imagePath,
				图片域名: imgDomain,
				标准化路径: normalizedPath,
				完整URL: fullUrl
			});
			
			return fullUrl;
		},
		
		// 验证图片URL是否可访问
		validateImageUrl(imageUrl) {
			// 创建一个临时的image元素来测试URL是否可访问
			if (typeof window !== 'undefined') {
				const testImg = new Image();
				testImg.onload = () => {
					console.log('图片URL验证成功:', imageUrl);
				};
				testImg.onerror = () => {
					console.error('图片URL验证失败:', imageUrl);
					uni.showToast({
						title: '图片可能无法正常显示',
						icon: 'none'
					});
				};
				testImg.src = imageUrl;
			}
		},
		
		// 图片加载成功
		onImageLoad(e) {
			console.log('图片加载成功:', this.imageUrl);
		},
		
		// 图片加载失败
		onImageError(e) {
			console.error('图片加载失败:', this.imageUrl);
			console.error('错误详情:', e);
			uni.showToast({
				title: '图片加载失败',
				icon: 'none'
			});
		},
		
		// 删除图片
		async deleteImage() {
			if (this.disabled) return;

			uni.showModal({
				title: '确认删除',
				content: '确定要删除这张图片吗？',
				success: async (res) => {
					if (res.confirm) {
						await this.performDelete();
					}
				}
			});
		},

		// 执行删除操作
		async performDelete() {
			const token = getToken();
			if (!token) {
				uni.showToast({
					title: '请先登录',
					icon: 'none'
				});
				return;
			}

			// 如果没有图片URL，直接清空
			if (!this.imageUrl) {
				this.clearImage();
				return;
			}

			uni.showLoading({
				title: '删除中...'
			});

			try {
				// 调用后端删除接口
				const response = await uni.request({
					url: uni.$u.http.config.baseURL + '/upload/deleteImage',
					method: 'POST',
					header: {
						'Authorization': `Bearer ${token}`,
						'Content-Type': 'application/json'
					},
					data: {
						path: this.imageUrl
					}
				});

				const data = response[1].data;

				if (data.status === 200 && data.code === 1) {
					// 删除成功，清空前端显示
					this.clearImage();
					uni.showToast({
						title: '删除成功',
						icon: 'success'
					});
				} else {
					throw new Error(data.msg || '删除失败');
				}

			} catch (error) {
				console.error('删除图片失败:', error);
				uni.showToast({
					title: error.message || '删除失败',
					icon: 'none'
				});
			} finally {
				uni.hideLoading();
			}
		},

		// 清空图片显示
		clearImage() {
			this.imageUrl = '';
			this.$emit('input', '');
			this.$emit('change', '', '');
			this.$emit('delete');
		}
	}
}
</script>

<style lang="scss" scoped>
.image-uploader {
	width: 100%;
}

.upload-container {
	border: 2rpx dashed #d1d5db;
	border-radius: 16rpx;
	background-color: #ffffff;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	transition: all 0.2s ease;
	
	&:active {
		transform: scale(0.98);
		background-color: #f9fafb;
	}
}

.upload-placeholder {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	text-align: center;
	padding: 32rpx;
	
	.upload-text {
		font-size: 28rpx;
		color: #6b7280;
		margin-top: 16rpx;
	}
	
	.upload-tip {
		font-size: 24rpx;
		color: #9ca3af;
		margin-top: 8rpx;
	}
}

.uploaded-image {
	width: 100%;
	border-radius: 12rpx;
}

.upload-mask {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.6);
	border-radius: 16rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	
	.upload-progress {
		color: #fff;
		font-size: 24rpx;
		margin-top: 16rpx;
	}
}

.delete-btn {
	position: absolute;
	top: 8rpx;
	right: 8rpx;
	width: 48rpx;
	height: 48rpx;
	background-color: rgba(0, 0, 0, 0.6);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	
	&:active {
		background-color: rgba(0, 0, 0, 0.8);
	}
}
</style>